@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  .loading-spinner {
    @apply relative h-16 w-16;
  }

  .loading-spinner::after,
  .loading-spinner::before {
    @apply content-[''] absolute h-full w-full rounded-full;
  }

  .loading-spinner::before {
    border: 4px solid rgba(255, 0, 159, 0.3);
  }

  .loading-spinner::after {
    border: 4px solid #ff009f;
    border-top-color: transparent;
    @apply animate-spin;
  }

  .loading-container {
    @apply min-h-screen bg-white pt-24 flex items-center justify-center;
  }
}

.hero-bg {
  background-image: linear-gradient(rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.1)),
    url("/hero.jpg");
}

:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;

  --ant-primary-color: #ff009f;
  --ant-primary-color-hover: #ff6b9f;
  --ant-primary-color-active: #d1007f;
  --ant-primary-color-outline: rgba(255, 0, 159, 0.2);
  --eigakan-primary: #ff009f;
  --eigakan-primary-hover: #d1007f;
  --eigakan-primary-disabled: rgba(0, 0, 0, 0.25);
  --eigakan-border-disabled: #d9d9d9;
  --eigakan-bg-disabled: #f5f5f5;
}

/* Note: Ken-burns animation is now managed in tailwind.config.js */
.animate-ken-burns {
  animation: slowZoom 20s ease-out forwards;
}

/* Similar Movies Swiper Navigation Styles */
.similar-movies-swiper .swiper-button-next,
.similar-movies-swiper .swiper-button-prev {
  color: white;
  background: rgba(0, 0, 0, 0.5);
  width: 40px;
  height: 40px;
  border-radius: 50%;
  transform: translateY(-50%);
}

.similar-movies-swiper .swiper-button-next:after,
.similar-movies-swiper .swiper-button-prev:after {
  font-size: 20px;
}

.similar-movies-swiper .swiper-button-disabled {
  opacity: 0;
  cursor: auto;
  pointer-events: none;
}

/* Custom Ant Design Spin color */
.ant-spin .ant-spin-dot-item {
  background-color: var(--eigakan-primary) !important;
}

/* Custom loading icon color */
.anticon.anticon-loading.anticon-spin {
  color: var(--eigakan-primary) !important;
}

/* Custom spin dot color */
.ant-spin-dot-spin > i {
  background-color: var(--eigakan-primary) !important;
}

.eigakan-glow {
  font-size: 2rem; /* Kích thước chữ */
  font-weight: bold;
  color: white; /* Màu chữ */
  text-transform: uppercase; /* Viết hoa toàn bộ */
  transition: text-shadow 0.3s ease-in-out;
}

.eigakan-glow:hover {
  text-shadow: 0 0 10px #facc15, 0 0 20px #facc15, 0 0 40px #facc15;
}

@keyframes gradientText {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.eigakan-gradient {
  font-size: 2rem;
  font-weight: bold;
  text-transform: uppercase;
  background: linear-gradient(90deg, #cc00ff, #ff009f, #7700ff);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: gradientText 5s infinite linear;
}

/* Custom Swiper Navigation Buttons */
.movie-list-swiper .swiper-button-next,
.movie-list-swiper .swiper-button-prev {
  @apply w-12 h-12 flex items-center justify-center;
  @apply bg-black/80 backdrop-blur-sm;
  @apply border-2 border-[#FF009F]/20;
  border-radius: 50%;
  @apply text-white;
  @apply transition-all duration-300;
  opacity: 0;
}

.movie-list-swiper:hover .swiper-button-next,
.movie-list-swiper:hover .swiper-button-prev {
  opacity: 1;
}

.movie-list-swiper .swiper-button-next:hover,
.movie-list-swiper .swiper-button-prev:hover {
  @apply bg-[#FF009F]/90;
  @apply border-white/20;
  @apply scale-110;
  @apply shadow-lg shadow-[#FF009F]/20;
}

.movie-list-swiper .swiper-button-next:after,
.movie-list-swiper .swiper-button-prev:after {
  @apply text-base font-bold;
}

.movie-list-swiper .swiper-button-disabled {
  opacity: 0 !important;
  cursor: not-allowed;
}

/* Loading animation - shimmer effect is defined in tailwind.config.js */
.loading-shimmer {
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0.03) 25%,
    rgba(255, 255, 255, 0.08) 50%,
    rgba(255, 255, 255, 0.03) 75%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

/* Override Ant Design Button styles */
.ant-btn {
  transition: all 0.2s ease-in-out !important;
}

/* Default button hover states */
.ant-btn:not(.ant-btn-primary):not(.ant-btn-link):not([disabled]):hover,
.ant-btn:not(.ant-btn-primary):not(.ant-btn-link):not([disabled]):focus {
  color: var(--eigakan-primary) !important;
  border-color: var(--eigakan-primary) !important;
  background-color: transparent !important;
}

/* Primary button hover states */
.ant-btn-primary:not([disabled]):hover,
.ant-btn-primary:not([disabled]):focus {
  background-color: var(--eigakan-primary-hover) !important;
  border-color: var(--eigakan-primary-hover) !important;
  color: white !important;
}

/* Link button hover states */
.ant-btn-link:not([disabled]):hover,
.ant-btn-link:not([disabled]):focus {
  color: var(--eigakan-primary-hover) !important;
  border-color: transparent !important;
  background: transparent !important;
}

/* Modal buttons */
.ant-modal-footer .ant-btn-primary {
  background-color: var(--eigakan-primary) !important;
  border-color: var(--eigakan-primary) !important;
}

.ant-modal-footer .ant-btn-primary:hover {
  background-color: var(--eigakan-primary-hover) !important;
  border-color: var(--eigakan-primary-hover) !important;
  box-shadow: 0 5px 15px var(--eigakan-primary-outline) !important;
}

.ant-modal-footer .ant-btn:not(.ant-btn-primary):hover {
  color: var(--eigakan-primary) !important;
  border-color: var(--eigakan-primary) !important;
}

/* Input hover and focus states */
.ant-input:hover,
.ant-input:focus,
.ant-input-focused,
.ant-input-affix-wrapper:hover,
.ant-input-affix-wrapper:focus,
.ant-input-affix-wrapper-focused {
  border-color: var(--eigakan-primary) !important;
  box-shadow: 0 0 0 2px var(--eigakan-primary-outline) !important;
}

/* Disabled states */
.ant-btn[disabled],
.ant-btn[disabled]:hover {
  color: var(--eigakan-primary-disabled) !important;
  border-color: var(--eigakan-border-disabled) !important;
  background: var(--eigakan-bg-disabled) !important;
  text-shadow: none !important;
  box-shadow: none !important;
}

/* Active states */
.ant-btn:active {
  color: var(--eigakan-primary-hover) !important;
  border-color: var(--eigakan-primary-hover) !important;
}

.ant-btn-primary:active {
  background-color: var(--eigakan-primary-hover) !important;
  color: white !important;
}

/* Thêm vào đầu file index.css */
html,
body,
#root {
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  background-color: black;
  width: 100vw !important;
  max-width: 100vw !important;
}

/* Loại bỏ mọi outline và border bởi User Agent stylesheet */
* {
  outline: none !important;
  box-sizing: border-box;
}

/* Đảm bảo navbar không có border */
header,
.navbar-wrapper {
  border: none !important;
  outline: none !important;
}

/* Add these specific rules for Ant Design Tabs to match the pink theme */

/* The tab ink bar (bottom indicator line) */
.ant-tabs-ink-bar {
  background-color: var(--eigakan-primary) !important;
  height: 2px !important; /* thinner line */
  border-radius: 0 !important; /* flat design */
  box-shadow: none !important; /* remove shadow */
  transition: all 0.2s ease !important;
}

/* Active tab text color - không cần font-weight bold */
.ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
  color: var(--eigakan-primary) !important;
  font-weight: normal !important;
  transform: none !important; /* remove the movement */
}

/* Tab hover color */
.ant-tabs-tab:hover .ant-tabs-tab-btn {
  color: var(--eigakan-primary-hover) !important;
}

/* For tabs with icons - ensure icons also get the right color */
.ant-tabs-tab.ant-tabs-tab-active .anticon {
  color: var(--eigakan-primary) !important;
  transform: none !important; /* remove the scale effect */
}

/* For tabs with icons - hover state */
.ant-tabs-tab:hover .anticon {
  color: var(--eigakan-primary-hover) !important;
}

/* Reset any conflicting styles */
.ant-tabs-tab.ant-tabs-tab-active {
  background-color: transparent !important;
  box-shadow: none !important;
  backdrop-filter: none !important;
}

/* Clean tab styling */
.ant-tabs-tab {
  transition: all 0.2s ease !important;
  padding: 8px 16px !important;
  margin: 0 2px !important;
  border-radius: 0 !important;
  position: relative;
  background: transparent !important;
}

/* Minimal hover effect */
.ant-tabs-tab:hover {
  background-color: transparent !important;
}

/* Simpler icon styling */
.ant-tabs-tab .anticon {
  margin-right: 6px;
  transition: all 0.2s ease;
}

/* Cleaner content area */
.ant-tabs-content-holder {
  border-top: 1px solid rgba(0, 0, 0, 0.06);
  padding-top: 12px;
}

/* Eigakan tabs styling - hợp nhất các quy tắc */
.eigakan-tabs .ant-tabs-nav {
  margin-bottom: 0;
}

.eigakan-tabs .ant-tabs-nav::before {
  border-bottom: none;
}

.eigakan-tabs .ant-tabs-tab {
  border-radius: 8px 8px 0 0;
  padding: 8px 16px;
  margin-right: 4px;
  transition: all 0.3s;
}

.eigakan-tabs .ant-tabs-tab:hover {
  color: var(--eigakan-primary);
}

.eigakan-tabs .ant-tabs-tab-active {
  background-color: #fff;
  border-bottom-color: #fff;
}

.eigakan-tabs .ant-tabs-tab-active .ant-tabs-tab-btn {
  color: var(--eigakan-primary);
  font-weight: normal;
}

/* Transaction details table styling */
.transaction-details .ant-descriptions-item-label {
  width: 180px;
}

/* Ad slots collapse styling */
.ad-slots-collapse .ant-collapse-header {
  padding: 12px 16px;
  background-color: #f9f9f9;
  border-radius: 8px !important;
}

.ad-slots-collapse .ant-collapse-content-box {
  padding: 16px;
}

/* For Select component hover/focus states */
.ant-select:hover .ant-select-selector,
.ant-select-focused .ant-select-selector {
  border-color: var(--eigakan-primary) !important;
}

/* For TimePicker component hover/focus states */
.ant-picker:hover,
.ant-picker-focused {
  border-color: var(--eigakan-primary) !important;
}

/* For InputNumber component hover/focus states */
.ant-input-number:hover,
.ant-input-number-focused {
  border-color: var(--eigakan-primary) !important;
}

/* For dropdown hover states */
.ant-select-dropdown .ant-select-item-option-active,
.ant-select-dropdown .ant-select-item-option-selected {
  background-color: rgba(255, 0, 159, 0.1) !important;
}

/* For any remaining blue focus rings */
*:focus {
  outline-color: var(--eigakan-primary) !important;
}

/* Thêm style cho logo để đảm bảo không bị animation */
.navbar-logo {
  /* Giữ nguyên animation gradient cho text, nhưng vô hiệu hóa các animation khác */
  transform: none !important;
  transition: none !important;
}

/* Hide scrollbar but maintain functionality */
.no-scrollbar {
  /* Hide scrollbars across browsers while keeping scroll functionality */
  -ms-overflow-style: none !important;
  scrollbar-width: none !important;
  overflow-y: auto !important;
}

/* Hide scrollbar for Webkit browsers (Chrome, Safari, Opera) */
.no-scrollbar::-webkit-scrollbar {
  display: none !important;
  width: 0 !important;
}

/* Apply to html and body too */
html.no-scrollbar,
body.no-scrollbar {
  overflow: hidden auto !important;
  margin-right: 0 !important;
  /* Prevent content shifting when scrollbar is hidden */
  padding-right: 0 !important;
  width: 100% !important;
}

/* Prevent any shift in the body width */
body.no-scrollbar {
  overflow-x: hidden !important;
  max-width: 100vw !important;
}
