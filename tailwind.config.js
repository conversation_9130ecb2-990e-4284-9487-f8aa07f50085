/** @type {import('tailwindcss').Config} */
import tailwindScrollbarHide from 'tailwind-scrollbar-hide';

export default {
  content: [
    "./index.html",
    "./src/**/*.{js,jsx}" // This line is important - it includes .jsx files
  ],
  theme: {
    extend: {
      animation: {
        'slow-zoom': 'slowZoom 20s linear infinite',
        'fade-up': 'fadeUp 0.5s ease-out forwards',
        'gradient-x': 'gradient-x 3s ease-in-out infinite',
        'shimmer': 'shimmer 2.5s infinite linear',
        'fadeIn': 'fadeIn 0.5s ease-in-out forwards',
      },
      keyframes: {
        slowZoom: {
          '0%': { transform: 'scale(1)' },
          '100%': { transform: 'scale(1.1)' },
        },
        fadeUp: {
          '0%': { opacity: '0', transform: 'translateY(20px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        'gradient-x': {
          '0%, 100%': {
            'background-size': '200% 200%',
            'background-position': 'left center'
          },
          '50%': {
            'background-size': '200% 200%',
            'background-position': 'right center'
          },
        },
        shimmer: {
          '0%': { transform: 'translateX(-100%)' },
          '100%': { transform: 'translateX(100%)' },
        },
      },
      backdropFilter: {
        'blur-navbar': 'blur(10px)',
      },
    },
  },
  plugins: [tailwindScrollbarHide],
}

