{"name": "eigakan-fe", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build --base=/", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@ant-design/compatible": "^5.1.4", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@microsoft/signalr": "^8.0.7", "@reduxjs/toolkit": "^2.6.0", "antd": "^5.23.1", "antd-img-crop": "^4.24.0", "apexcharts": "^4.3.0", "axios": "^1.7.9", "canvas-confetti": "^1.9.3", "chart.js": "^4.4.8", "date-fns": "^4.1.0", "framer-motion": "^11.18.2", "hls.js": "^1.5.20", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "lucide-react": "^0.469.0", "moment": "^2.30.1", "peerjs": "^1.5.4", "react": "^18.3.1", "react-apexcharts": "^1.7.0", "react-chartjs-2": "^5.3.0", "react-copy-to-clipboard": "^5.1.0", "react-dom": "^18.3.1", "react-easy-crop": "^5.4.1", "react-helmet": "^6.1.0", "react-hot-toast": "^2.5.1", "react-icons": "^5.4.0", "react-intersection-observer": "^9.15.0", "react-modal": "^3.16.3", "react-pdf": "^9.2.1", "react-player": "^2.16.0", "react-quill": "^2.0.0", "react-redux": "^9.2.0", "react-router-dom": "^7.1.1", "recharts": "^2.15.1", "screenfull": "^6.0.2", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "swiper": "^11.2.5", "tailwind-scrollbar-hide": "^2.0.0", "uuid": "^11.1.0", "zustand": "^5.0.2"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "eslint": "^9.17.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "postcss": "^8.5.1", "tailwindcss": "^3.4.17", "vite": "^6.0.5"}}